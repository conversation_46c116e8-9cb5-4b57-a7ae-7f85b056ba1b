<template>
  <div class="irrigation-section">
    <div class="section-header">
      <h3 class="section-title">{{ title }}</h3>
      <a-button type="primary" @click="handleSelectChannel">选择渠道</a-button>
    </div>
    <vxe-table
      :ref="tableRef"
      :data="tableData"
      border
      stripe
      :row-config="{ keyField: 'id' }"
      :column-config="{ resizable: true }"
      :edit-config="{ trigger: 'manual', mode: 'cell', showStatus: true }"
      height="350"
      class="irrigation-table"
      width="100%"
      :footer-data="computedFooterData"
      :show-footer="showFooter"
      header-align="center"
      align="center"
    >
      <!-- 渠道列 -->
      <vxe-column field="channel" title="渠道\作物" width="120">
        <template #default="{ row }">
          <span v-if="row.channel" :title="row.channelCode">
            {{ row.channel }}
          </span>
          <span v-else style="color: #999; font-style: italic;">
            请选择渠道
          </span>
        </template>
      </vxe-column>

      <!-- 动态渲染表头 -->
      <template v-for="column in tableColumns" >
        <!-- 分组列 -->
        <vxe-colgroup v-if="column.type === 'group'" :title="column.title">
          <template v-for="subColumn in column.children" >
            <!-- 子分组列 -->
            <vxe-colgroup v-if="subColumn.type === 'group'" :title="subColumn.title">
              <template v-for="subSubColumn in subColumn.children" >
                <vxe-column 
                  v-if="subSubColumn.field"
                  :field="subSubColumn.field" 
                  :title="subSubColumn.title" 
                  :width="subSubColumn.width"
                >
                  <template #default="{ row }">
                    <a-input-number
                      v-if="subSubColumn.editable"
                      v-model="row[subSubColumn.field]"
                      :precision="2"
                      :min="0"
                      style="width: 100%"
                      @change="handleCropChange(row, subSubColumn)"
                    />
                    <span v-else-if="subSubColumn.isSubtotal" :style="{ fontWeight: 'bold', color: '#1890ff' }">
                      {{ formatNumber(row[subSubColumn.field]) }}
                    </span>
                    <span v-else>
                      {{ formatNumber(row[subSubColumn.field]) }}
                    </span>
                  </template>
                </vxe-column>
              </template>
            </vxe-colgroup>
            <!-- 普通子列 -->
            <vxe-column 
              v-else-if="subColumn.field"
              :field="subColumn.field" 
              :title="subColumn.title" 
              :width="subColumn.width"
            >
              <template #default="{ row }">
                <a-input-number
                  v-if="subColumn.editable"
                  v-model="row[subColumn.field]"
                  :precision="2"
                  :min="0"
                  style="width: 100%"
                  @change="handleCropChange(row, subColumn)"
                />
                <span v-else-if="subColumn.isSubtotal" :style="{ fontWeight: 'bold', color: '#52c41a' }">
                  {{ formatNumber(row[subColumn.field]) }}
                </span>
                <span v-else>
                  {{ formatNumber(row[subColumn.field]) }}
                </span>
              </template>
            </vxe-column>
          </template>
        </vxe-colgroup>
        <!-- 普通列 -->
        <vxe-column 
          v-else
          :field="column.field" 
          :title="column.title" 
          :width="column.width"
        >
          <template #default="{ row }">
            <a-input-number
              v-if="column.editable"
              v-model="row[column.field]"
              :precision="2"
              :min="0"
              style="width: 100%"
            />
            <span v-else-if="column.isTotal" :style="{ fontWeight: 'bold', color: '#52c41a' }">
              {{ formatNumber(row[column.field]) }}
            </span>
            <span v-else>
              {{ formatNumber(row[column.field]) }}
            </span>
          </template>
        </vxe-column>
      </template>
    </vxe-table>

    <!-- 选择渠道弹窗 -->
    <ChannelSelectModal
      ref="channelSelectModal"
      @ok="handleChannelSelectOk"
    />
  </div>
</template>

<script>
import ChannelSelectModal from './ChannelSelectModal.vue'
import { plantingCrop, getLeafCropsWithFieldNames, getFieldNameToValueMap } from '@/enum/planting-crop'
import { getOptions } from '@/api/common'
import user from '@/store/modules/user'
export default {
  name: 'IrrigationPlantingTable',
  components: {
    ChannelSelectModal
  },
  props: {
    title: {
      type: String,
      required: true
    },
    tableData: {
      type: Array,
      default: () => []
    },
    tableRef: {
      type: String,
      default: 'tableRef'
    }
  },
  data() {
    return {
      // 作物分类数据
      plantingCropCategories: [],
      // 农作物字典选项
      cropOptions: [],
      // 字段名到作物key的映射
      fieldNameToCropKeyMap: {},
      // 表格列配置
      tableColumns: [],
      // 合计行数据
      footerData: [
        {
          channel: '合计'
        }
      ]
    }
  },
  computed: {
    // 计算合计值
    computedFooterData() {
      // 如果表格数据为空，返回空数组
      if (this.tableData.length === 0) {
        return []
      }
      
      const totalRow = { channel: '合计' }
      
      // 获取所有需要计算合计的字段
      const leafCrops = getLeafCropsWithFieldNames()
      const fields = leafCrops.map(crop => crop.fieldName).filter(Boolean)
      
      // 添加其他字段
      fields.push('farmlandArea', 'totalIrrigationArea')
      
      // 添加小计字段
      this.plantingCropCategories.forEach(category => {
        const subtotalField = this.getSubtotalFieldName(category)
        if (subtotalField) {
          fields.push(subtotalField)
        }
        
        if (category.children) {
          category.children.forEach(subCategory => {
            if (subCategory.children && subCategory.children.length > 0) {
              const subSubtotalField = this.getSubtotalFieldName(subCategory)
              if (subSubtotalField) {
                fields.push(subSubtotalField)
              }
            }
          })
        }
      })

      fields.forEach(field => {
        let total = 0
        this.tableData.forEach(row => {
          total += parseFloat(row[field]) || 0
        })
        totalRow[field] = total > 0 ? total.toFixed(2) : null
      })

      return [totalRow]
    },
    showFooter() {
      return this.tableData.length > 0
    }
  },
  created() {
    this.initPlantingCropCategories()
    this.loadCropOptions()
    this.initTableColumns()
  },
  mounted() {
    // 调试信息
    this.debugMappings()
  },
  methods: {
    // 初始化表格列配置
    initTableColumns() {
      const columns = []
      
      // 动态生成作物分类列（排除热水地和干地）
      this.plantingCropCategories.forEach((category, index) => {
        if (category.children) {
          // 有子分类的情况，创建分组列
          const groupColumn = {
            type: 'group',
            title: category.label,
            children: []
          }
          
          category.children.forEach(subCategory => {
            if (subCategory.children && subCategory.children.length > 0) {
              // 有三级分类，创建子分组
              const subGroupColumn = {
                type: 'group',
                title: subCategory.label,
                children: []
              }
              
              // 添加三级分类的列
              subCategory.children.forEach(crop => {
                if (crop.fieldName) {
                  subGroupColumn.children.push({
                    field: crop.fieldName,
                    title: crop.label,
                    width: 70,
                    editable: true,
                    cropInfo: crop
                  })
                }
              })
              
              // 添加子分组小计列
              const subSubtotalField = this.getSubtotalFieldName(subCategory)
              if (subSubtotalField) {
                subGroupColumn.children.push({
                  field: subSubtotalField,
                  title: '小计',
                  width: 70,
                  editable: false,
                  isSubtotal: true
                })
              }
              
              groupColumn.children.push(subGroupColumn)
            } else if (subCategory.fieldName) {
              // 没有三级分类，直接添加为列
              groupColumn.children.push({
                field: subCategory.fieldName,
                title: subCategory.label,
                width: 80,
                editable: true,
                cropInfo: subCategory
              })
            }
          })
          
          // 添加大分类小计列
          const subtotalField = this.getSubtotalFieldName(category)
          if (subtotalField) {
            groupColumn.children.push({
              field: subtotalField,
              title: '小计',
              width: 80,
              editable: false,
              isSubtotal: true
            })
          }
          
          columns.push(groupColumn)
          
          // 在秋季作物后添加耕地面积列
          if (category.value === 'AUTUMN_CROPS') {
            columns.push({
              field: 'farmlandArea',
              title: '耕地面积',
              width: 100,
              editable: false
            })
          }
        }
      })
      
      // 添加总灌溉面积列
      columns.push({
        field: 'totalIrrigationArea',
        title: '总灌溉面积',
        width: 120,
        editable: false,
        isTotal: true
      })
      
      // 添加热水地和干地列（放在最右侧）
      const hotWaterLand = plantingCrop.find(crop => crop.value === 'HOT_WATER_LAND')
      const dryLand = plantingCrop.find(crop => crop.value === 'DRY_LAND')
      
      if (hotWaterLand && hotWaterLand.fieldName) {
        columns.push({
          field: hotWaterLand.fieldName,
          title: hotWaterLand.label,
          width: 80,
          editable: true,
          cropInfo: hotWaterLand
        })
      }
      
      if (dryLand && dryLand.fieldName) {
        columns.push({
          field: dryLand.fieldName,
          title: dryLand.label,
          width: 80,
          editable: true,
          cropInfo: dryLand
        })
      }
      
      this.tableColumns = columns
    },

    // 初始化作物分类数据
    initPlantingCropCategories() {
      // 过滤出有子分类的作物（排除热水地和干地）
      this.plantingCropCategories = plantingCrop.filter(category => 
        !['HOT_WATER_LAND', 'DRY_LAND'].includes(category.value)
      )
    },

    // 加载作物选项字典
    async loadCropOptions() {
      try {
        const response = await getOptions('crop')
        if (response.code === 200 && response.data) {
          this.cropOptions = response.data.map(item => ({
            key: item.key,
            value: item.value,
            label: item.value
          }))
          
          // 创建字段名到作物key的映射
          this.createFieldNameToCropKeyMap()
          
          // 测试匹配功能
          this.testCropMatching()
        } else {
          console.error('获取作物选项失败:', response.message)
        }
      } catch (error) {
        console.error('获取作物选项失败:', error)
      }
    },

    // 创建字段名到作物key的映射
    createFieldNameToCropKeyMap() {
      const fieldNameToValueMap = getFieldNameToValueMap()
      
      Object.keys(fieldNameToValueMap).forEach(fieldName => {
        const cropValue = fieldNameToValueMap[fieldName]
        const leafCrop = getLeafCropsWithFieldNames().find(crop => crop.value === cropValue)
        
        if (leafCrop) {
          // 通过label匹配字典中的项
          const matchedOption = this.cropOptions.find(option => 
            option.label === leafCrop.label || option.value === leafCrop.label
          )
          
          if (matchedOption) {
            this.fieldNameToCropKeyMap[fieldName] = matchedOption.key
          }
        }
      })
      
    },

    // 调试映射关系
    debugMappings() {
      
      // 显示叶子节点作物
      const leafCrops = getLeafCropsWithFieldNames()
      
      // 显示字段名到value的映射
      const fieldNameToValueMap = getFieldNameToValueMap()
    },

    // 测试作物字典匹配
    testCropMatching() {
      
      // 测试几个字段名
      const testFields = ['wheat', 'corn', 'tomato', 'matureForest', 'grassland']
      
      testFields.forEach(fieldName => {
        const cropKey = this.getCropKey(fieldName)
        const cropInfo = this.fieldNameToCropKeyMap[fieldName]
        
        // 显示完整的匹配信息
        const fieldNameToValueMap = getFieldNameToValueMap()
        const cropValue = fieldNameToValueMap[fieldName]
        if (cropValue) {
          const leafCrop = getLeafCropsWithFieldNames().find(crop => crop.value === cropValue)
          const matchedOption = this.cropOptions.find(option => 
            option.label === leafCrop?.label || option.value === leafCrop?.label
          )
        }
      })
    },

    // 获取作物显示名称
    getCropDisplayName(crop) {
      return crop.label
    },

    // 获取小计字段名
    getSubtotalFieldName(category) {
      const subtotalMap = {
        'SUMMER_CROPS': 'summerCropsSubtotal',
        'AUTUMN_CROPS': 'autumnCropsSubtotal',
        'FOREST_PASTURE': 'forestGrasslandSubtotal',
        'MELON_VEGETABLES': 'melonVegetableSubtotal',
        'WHEAT_CATEGORY': 'wheatIntercroppingSubtotal',
        'FOREST_LAND': 'forestlandSubtotal'
      }
      return subtotalMap[category.value] || null
    },

    // 处理作物数据变化
    handleCropChange(row, column) {
      // 根据作物所属分类重新计算相关小计
      if (column.cropInfo) {
        this.recalculateSubtotals(row, column.cropInfo)
      }
      // 热水地和干地不参与总灌溉面积计算，所以不需要重新计算
      this.$emit('row-change', row)
    },

    // 重新计算小计
    recalculateSubtotals(row, crop) {
      // 根据作物的分类重新计算对应的小计
      if (crop.firstTypeCode === '1') {
        // 夏季作物
        if (crop.secondTypeCode === '1') {
          // 瓜菜
          this.calculateMelonVegetables(row)
        } else if (crop.secondTypeCode === '2') {
          // 小麦间套种
          this.calculateWheatIntercropping(row)
        } else {
          // 其他夏季作物
          this.calculateSummerCrops(row)
        }
      } else if (crop.firstTypeCode === '2') {
        // 秋季作物
        this.calculateAutumnCrops(row)
      } else if (crop.firstTypeCode === '3') {
        // 林牧地
        if (crop.secondTypeCode === '3') {
          // 林地
          this.calculateForestland(row)
        } else {
          // 牧草地
          this.calculateForestGrassland(row)
        }
      }
      // 热水地和干地不参与总灌溉面积计算，不需要特殊处理
    },

    // 计算瓜菜小计
    calculateMelonVegetables(row) {
      const melon = parseFloat(row.melon) || 0
      const sunflowerInMelon = parseFloat(row.sunflowerInMelon) || 0
      const tomato = parseFloat(row.tomato) || 0
      row.melonVegetableSubtotal = (melon + sunflowerInMelon + tomato).toFixed(2)
      this.calculateSummerCrops(row)
    },

    // 计算小麦间套种小计
    calculateWheatIntercropping(row) {
      const corn = parseFloat(row.corn) || 0
      const pepper = parseFloat(row.pepper) || 0
      const sunflowerInWheat = parseFloat(row.sunflowerInWheat) || 0
      const otherInWheat = parseFloat(row.otherInWheat) || 0
      row.wheatIntercroppingSubtotal = (corn + pepper + sunflowerInWheat + otherInWheat).toFixed(2)
      this.calculateSummerCrops(row)
    },

    // 计算夏季作物小计
    calculateSummerCrops(row) {
      const wheat = parseFloat(row.wheat) || 0
      const oilCrop = parseFloat(row.oilCrop) || 0
      const summerMisc = parseFloat(row.summerMisc) || 0
      const melonVegetableSubtotal = parseFloat(row.melonVegetableSubtotal) || 0
      const wheatIntercroppingSubtotal = parseFloat(row.wheatIntercroppingSubtotal) || 0
      row.summerCropsSubtotal = (wheat + oilCrop + summerMisc + melonVegetableSubtotal + wheatIntercroppingSubtotal).toFixed(2)
      this.calculateFarmlandArea(row)
    },

    // 计算秋季作物小计
    calculateAutumnCrops(row) {
      const autumnCorn = parseFloat(row.autumnCorn) || 0
      const gourd = parseFloat(row.gourd) || 0
      const autumnPepper = parseFloat(row.autumnPepper) || 0
      const dehydratedVegetables = parseFloat(row.dehydratedVegetables) || 0
      const autumnSunflower = parseFloat(row.autumnSunflower) || 0
      const autumnMisc = parseFloat(row.autumnMisc) || 0
      row.autumnCropsSubtotal = (autumnCorn + gourd + autumnPepper + dehydratedVegetables + autumnSunflower + autumnMisc).toFixed(2)
      this.calculateFarmlandArea(row)
    },

    // 计算耕地面积（夏季作物小计 + 秋季作物小计）
    calculateFarmlandArea(row) {
      const summerCropsSubtotal = parseFloat(row.summerCropsSubtotal) || 0
      const autumnCropsSubtotal = parseFloat(row.autumnCropsSubtotal) || 0
      row.farmlandArea = (summerCropsSubtotal + autumnCropsSubtotal).toFixed(2)
      this.calculateTotalIrrigationArea(row)
    },

    // 计算林地小计
    calculateForestland(row) {
      const matureForest = parseFloat(row.matureForest) || 0
      const youngForest = parseFloat(row.youngForest) || 0
      const fruitTree = parseFloat(row.fruitTree) || 0
      const wolfberry = parseFloat(row.wolfberry) || 0
      row.forestlandSubtotal = (matureForest + youngForest + fruitTree + wolfberry).toFixed(2)
      this.calculateForestGrassland(row)
    },

    // 计算林牧地小计
    calculateForestGrassland(row) {
      const forestlandSubtotal = parseFloat(row.forestlandSubtotal) || 0
      const grassland = parseFloat(row.grassland) || 0
      row.forestGrasslandSubtotal = (forestlandSubtotal + grassland).toFixed(2)
      this.calculateTotalIrrigationArea(row)
    },

    // 计算总灌溉面积
    calculateTotalIrrigationArea(row) {
      const farmlandArea = parseFloat(row.farmlandArea) || 0
      const forestGrasslandSubtotal = parseFloat(row.forestGrasslandSubtotal) || 0
      row.totalIrrigationArea = (farmlandArea + forestGrasslandSubtotal).toFixed(2)
      this.$emit('row-change', row)
    },

    // 获取作物对应的字典key
    getCropKey(fieldName) {
      return this.fieldNameToCropKeyMap[fieldName] || null
    },

    // 获取所有作物字段的字典key映射
    getAllCropKeys() {
      return { ...this.fieldNameToCropKeyMap }
    },

    // 获取作物字段信息
    getCropFieldInfo(fieldName) {
      const fieldNameToValueMap = getFieldNameToValueMap()
      const cropValue = fieldNameToValueMap[fieldName]
      const leafCrop = getLeafCropsWithFieldNames().find(crop => crop.value === cropValue)
      const matchedOption = this.cropOptions.find(option => 
        option.label === leafCrop?.label || option.value === leafCrop?.label
      )
      
      return {
        fieldName,
        cropValue,
        cropLabel: leafCrop?.label,
        cropKey: matchedOption?.key,
        matchedOption
      }
    },

    // 选择渠道
    handleSelectChannel() {
      console.log('选择渠道按钮被点击', this.tableData)
      // 获取当前已选择的渠道
      const selectedChannels = this.tableData.map(row => ({
        id: row.projectId || row.id,
        projectId: row.projectId || row.id,
        channelCode: row.channelCode,
        channelName: row.channel,
        projectCode: row.channelCode,
        projectName: row.channel
      })).filter(item => item.channelCode && item.channelName)
      
      console.log('传递给弹窗的已选渠道:', selectedChannels)
      this.$refs.channelSelectModal.show(selectedChannels)
    },

    // 处理渠道选择确认
    handleChannelSelectOk(selectedChannels) {
      console.log('选择的渠道:', selectedChannels)
      
      // 根据选择的渠道更新表格数据
      const newTableData = selectedChannels.map((channel, index) => {
        // 查找是否已存在该渠道的数据
        const existingRow = this.tableData.find(row => row.channelCode === channel.channelCode)
        
        if (existingRow) {
          // 如果已存在，保留原有数据，更新渠道信息
          return { 
            ...existingRow, 
            id: channel.id,
            projectId: channel.projectId,
            channelCode: channel.channelCode,
            channel: channel.channelName 
          }
        } else {
          // 如果不存在，创建新行
          const newRow = {
            id: channel.id,
            projectId: channel.projectId,
            channelCode: channel.channelCode,
            channel: channel.channelName,
            farmlandArea: null,
            totalIrrigationArea: null
          }
          
          // 为所有作物字段初始化null值
          const leafCrops = getLeafCropsWithFieldNames()
          leafCrops.forEach(crop => {
            if (crop.fieldName) {
              newRow[crop.fieldName] = null
            }
          })
          
          // 初始化小计字段
          const subtotalFields = [
            'melonVegetableSubtotal', 'wheatIntercroppingSubtotal', 'summerCropsSubtotal',
            'autumnCropsSubtotal', 'forestlandSubtotal', 'forestGrasslandSubtotal'
          ]
          subtotalFields.forEach(field => {
            newRow[field] = null
          })
          
          return newRow
        }
      })
      
      console.log('更新后的表格数据:', newTableData)
      this.$emit('update-table-data', newTableData)
    },

    // 格式化数字显示
    formatNumber(value) {
      if (value === null || value === undefined || value === '') {
        return '-'
      }
      const num = parseFloat(value)
      if (isNaN(num) || num === 0) {
        return '-'
      }
      return num.toFixed(2)
    }
  }
}
</script>

<style lang="less" scoped>
.irrigation-section {
  margin-bottom: 20px;

  .section-header {
    display: flex;
    // justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    gap: 10px;
  }

  .section-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
  }

  .irrigation-table {
    /deep/ .vxe-header--column {
      font-weight: bold;
    }

    /deep/ .vxe-cell {
      padding: 2px 4px;
    }

    /deep/ .vxe-input {
      padding: 2px 4px;
    }
  }
}
</style> 