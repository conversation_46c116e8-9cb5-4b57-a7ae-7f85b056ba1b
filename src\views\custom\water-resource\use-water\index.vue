<template>
  <div class="common-table-page">
    <!-- 筛选栏 -->
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="时间维度">
        <a-radio-group v-model="queryParam.planType" @change="handleQuery">
          <a-radio-button v-for="item in radioOptions" :key="item.value" :value="item.value">
            {{ item.label }}
          </a-radio-button>
        </a-radio-group>
      </a-form-item>

      <a-form-item label="上报单位">
        <a-tree-select
          style="max-width: 200px"
          v-model="queryParam.deptId"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          :tree-data="deptOptions"
          treeNodeFilterProp="title"
          allowClear
          placeholder="请选择"
          :replaceFields="{
            children: 'children',
            title: 'deptName',
            key: 'deptId',
            value: 'deptId',
          }"
          tree-default-expand-all
        ></a-tree-select>
      </a-form-item>
      <a-form-item label="计划时间">
        <a-range-picker
          allow-clear
          style="width: 100%"
          v-model="rangeDate"
          :placeholder="['开始时间', '结束时间']"
          :format="queryParam.planType === 3 ? 'YYYY-MM' : 'YYYY-MM-DD'"
          :mode="queryParam.planType === 3 ? ['month', 'month'] : ['date', 'date']"
          @panelChange="handlePanelChange"
        />
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          tableTitle="用水计划上报"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          @sortChange="sortChange"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="danger" v-if="isChecked" @click="handleDelete">
              <a-icon type="delete" />
              删除
            </a-button>
            <a-button type="primary" @click="handleExport()" :loading="exportLoading">
              <a-icon type="download" />
              导出
            </a-button>
          </div>
        </VxeTable>

        <SegmentFormModal
          v-if="showSegmentFormModal"
          :radioOptions="radioOptions"
          :deptType="deptType"
          ref="segmentFormModalRef"
          @ok="getList"
          @close="showSegmentFormModal = false"
        />

        <OfficeFormModal
          v-if="showOfficeFormModal"
          :radioOptions="radioOptions"
          :deptType="deptType"
          ref="officeFormModalRef"
          @ok="getList"
          @close="showOfficeFormModal = false"
        />

        <CenterFormModal
          v-if="showCenterFormModal"
          :radioOptions="radioOptions"
          ref="centerFormModalRef"
          @ok="getList"
          @close="showCenterFormModal = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getPlanReportPage, deletePlanReport, withdrawUseWater } from './services'
  import { getUserDeptType } from '@/api/user'
  import { getValueByKey, getTreeByLoginOrgId } from '@/api/common'
  import SegmentFormModal from './modules/SegmentFormModal.vue'
  import OfficeFormModal from './modules/OfficeFormModal.vue'
  import CenterFormModal from './modules/CenterFormModal.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import excelExport from '@/utils/excelExport.js'
  import moment from 'moment'

  export default {
    name: 'UseWater',
    components: {
      VxeTableForm,
      VxeTable,
      SegmentFormModal,
      OfficeFormModal,
      CenterFormModal,
    },
    data() {
      return {
        showOfficeFormModal: false,
        showSegmentFormModal: false,
        showCenterFormModal: false,
        deptOptions: [],
        exportLoading: false,
        radioOptions: [
          { label: '5日', value: 1 },
          { label: '旬', value: 2 },
          { label: '月', value: 3 },
        ],

        showDetailsWaterLedger: false,
        schedulingTypeOptions: [
          { label: '灌溉调度', value: 1 },
          { label: '防汛调度', value: 2 },
        ],

        deptType: undefined,

        list: [],
        isChecked: false,
        ids: [],
        names: [],
        loading: false,
        total: 0,

        rangeDate: [],
        queryParam: {
          planType: 1,
          deptId: undefined,

          pageNum: 1,
          pageSize: 10,
        },
        columns: [
          { type: 'checkbox', width: 40 },
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '计划编号',
            field: 'planNumber',
            minWidth: 130,
            showOverflow: true,
          },
          {
            title: '计划名称',
            field: 'planName',
            minWidth: 130,
            showOverflow: true,
          },
          {
            title: '上报单位',
            field: 'deptName',
            minWidth: 100,
          },
          {
            title: '计划时间',
            minWidth: 170,
            slots: {
              default: ({ row }) => `${row.planStartDate} ~ ${row.planEndDate}`,
            },
          },
          {
            title: '上报时间',
            field: 'createdTime',
            minWidth: 120,
            showOverflow: true,
          },
          {
            title: '操作',
            field: 'operate',
            width: 140,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDetails(row)}>查看</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleUpdate(row)}>修改</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleDelete(row)}>删除</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    computed: {},
    watch: {},
    beforeDestroy() {},
    created() {
      getTreeByLoginOrgId().then(res => {
        this.deptOptions = res?.data
      })

      getUserDeptType({ deptId: JSON.parse(localStorage.getItem('user')).deptId }).then(resp => {
        this.deptType = resp.data
      })
    },
    methods: {
      handlePanelChange(value, mode) {
        this.rangeDate = value
      },
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },

      /** 查询列表 */
      getList() {
        this.showSegmentFormModal = false
        this.loading = true
        this.selectChange({ records: [] })
        getPlanReportPage({
          ...this.queryParam,
          planStartDate: this.rangeDate[0]?.format('YYYY-MM-DD'),
          planEndDate: this.rangeDate[1]?.format('YYYY-MM-DD'),
        }).then(response => {
          this.list = response?.data?.data

          this.total = response?.data?.total
          this.loading = false
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.rangeDate = []
        this.queryParam = {
          ...this.queryParam,
          deptId: undefined,
        }
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.planReportId)
        this.names = valObj.records.map(item => item.serialNumber)
        this.isChecked = !!valObj.records.length
      },
      // 排序
      sortChange(valObj) {
        this.queryParam.sort = valObj.sortList.map(item => ({ property: item.field, direction: item.order }))
        this.getList()
      },
      /* 新增 */
      handleAdd() {
        if (this.deptType === 3) {
          this.showSegmentFormModal = true
          this.$nextTick(() => this.$refs.segmentFormModalRef.handleAdd())
        }
        if (this.deptType === 2) {
          this.showOfficeFormModal = true
          this.$nextTick(() => this.$refs.officeFormModalRef.handleAdd())
        }
        if (this.deptType === 1) {
          this.showCenterFormModal = true
          this.$nextTick(() => this.$refs.centerFormModalRef.handleAdd())
        }
      },
      /* 修改 */
      handleUpdate(record) {
        if (this.deptType === 3) {
          this.showSegmentFormModal = true
          this.$nextTick(() => this.$refs.segmentFormModalRef.handleUpdate(record))
        }
        if (this.deptType === 2) {
          this.showOfficeFormModal = true
          this.$nextTick(() => this.$refs.officeFormModalRef.handleAdd(record))
        }
        if (this.deptType === 1) {
          this.showCenterFormModal = true
          this.$nextTick(() => this.$refs.centerFormModalRef.handleAdd(record))
        }
      },
      //查看
      handleDetails(record) {
        if (this.deptType === 3) {
          this.showSegmentFormModal = true
          this.$nextTick(() => this.$refs.segmentFormModalRef.handleUpdate({ ...record, isDetail: true }))
        }
        if (this.deptType === 2) {
          this.showOfficeFormModal = true
          this.$nextTick(() => this.$refs.officeFormModalRef.handleAdd({ ...record, isDetail: true }))
        }
        if (this.deptType === 1) {
          this.showCenterFormModal = true
          this.$nextTick(() => this.$refs.centerFormModalRef.handleAdd({ ...record, isDetail: true }))
        }
      },
      /** 导出按钮操作 */
      handleExport() {
        this.exportLoading = true
        getPlanReportPage({
          ...this.queryParam,
          planStartDate: this.rangeDate[0]?.format('YYYY-MM-DD'),
          planEndDate: this.rangeDate[1]?.format('YYYY-MM-DD'),
          pageSize: Number.MAX_SAFE_INTEGER,
          pageNum: 1,
        }).then(response => {
          this.exportLoading = false
          excelExport(
            this.columns.slice(2, this.columns.length - 1),
            response?.data?.data,
            `${this.$route.meta.title}${moment().format('YYYYMMDDHHmmss')}`,
          )
        })
      },

      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const planReportIds = row.planReportId ? [row.planReportId] : this.ids
        const names = row.serialNumber || this.names

        this.$confirm({
          title: '确认删除所选中数据?',
          content: '当前选中编号为"' + names + '"的数据',
          onOk() {
            return deletePlanReport({ planReportIds: planReportIds.join(',') }).then(res => {
              that.$message.success(`成功删除 ${res.data} 条数据`, 3)
              that.selectChange({ records: [] })
              that.getList()
            })
          },
          onCancel() {},
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .table-operations {
    .ant-btn {
      &:last-child {
        margin-right: 0px;
      }
    }
  }
</style>
